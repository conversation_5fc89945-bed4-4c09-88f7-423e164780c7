import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { BaseNodeExecutor } from '../base/base-node.executor';
import { 
  Executor<PERSON>ontext, 
  ValidationResult,
  DetailedNodeExecutionResult 
} from '../base/node-executor.interface';
import { NodeExecutionConfig } from '../../types';
import { NodeGroupEnum } from '../../../../enums/node-group.enum';

/**
 * HTTP request configuration
 */
interface HttpRequestConfig {
  /** HTTP method */
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';
  
  /** Request URL */
  url: string;
  
  /** Request headers */
  headers?: Record<string, string>;
  
  /** Query parameters */
  params?: Record<string, any>;
  
  /** Request body */
  body?: any;
  
  /** Request timeout (ms) */
  timeout?: number;
  
  /** Follow redirects */
  followRedirects?: boolean;
  
  /** Max redirects */
  maxRedirects?: number;
  
  /** Validate SSL certificates */
  validateSSL?: boolean;
  
  /** User agent */
  userAgent?: string;
  
  /** Authentication */
  auth?: {
    type: 'basic' | 'bearer' | 'api-key';
    username?: string;
    password?: string;
    token?: string;
    apiKey?: string;
    apiKeyHeader?: string;
  };
  
  /** Retry configuration */
  retry?: {
    enabled: boolean;
    maxAttempts: number;
    delay: number;
    backoffStrategy: 'fixed' | 'exponential' | 'linear';
    retryOn: number[]; // HTTP status codes to retry on
  };
  
  /** Response handling */
  response?: {
    /** Expected content type */
    expectedContentType?: string;
    
    /** Parse JSON automatically */
    parseJSON?: boolean;
    
    /** Include response headers in output */
    includeHeaders?: boolean;
    
    /** Include status code in output */
    includeStatusCode?: boolean;
    
    /** Success status codes */
    successCodes?: number[];
  };
}

/**
 * HTTP response data
 */
interface HttpResponseData {
  /** Response status code */
  statusCode: number;
  
  /** Response status text */
  statusText: string;
  
  /** Response headers */
  headers?: Record<string, string>;
  
  /** Response body */
  data: any;
  
  /** Response size in bytes */
  size?: number;
  
  /** Response time in milliseconds */
  responseTime: number;
  
  /** Final URL (after redirects) */
  finalUrl?: string;
  
  /** Redirect count */
  redirectCount?: number;
}

/**
 * Executor cho HTTP nodes - handle HTTP requests, API calls, webhooks
 */
@Injectable()
export class HttpNodeExecutor extends BaseNodeExecutor {
  readonly nodeGroup = NodeGroupEnum.HTTP;
  readonly supportedNodeTypes = [
    'http-request',
    'api-call',
    'webhook',
    'rest-api',
    'graphql',
    'soap',
    'http-get',
    'http-post',
    'http-put',
    'http-delete',
    'http-patch',
  ];
  readonly executorName = 'HttpNodeExecutor';
  readonly version = '1.0.0';
  
  constructor(private readonly httpService: HttpService) {
    super();
  }
  
  /**
   * Execute HTTP node
   */
  protected async executeNode(
    context: ExecutorContext,
    config: NodeExecutionConfig
  ): Promise<DetailedNodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Parse HTTP configuration từ node parameters
      const httpConfig = this.parseHttpConfig(context);
      
      // Prepare request
      const requestConfig = await this.prepareRequest(httpConfig, context);
      
      // Execute HTTP request với retry logic
      const response = await this.executeHttpRequest(requestConfig, config);
      
      // Process response
      const outputData = this.processResponse(response, httpConfig);
      
      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        outputData,
        metadata: {
          executionTime,
          httpStatusCode: response.statusCode,
          responseHeaders: httpConfig.response?.includeHeaders ? response.headers : undefined,
          customMetrics: {
            responseTime: response.responseTime,
            responseSize: response.size,
            redirectCount: response.redirectCount || 0,
            finalUrl: response.finalUrl,
          },
          logs: [
            `HTTP ${httpConfig.method} request to ${httpConfig.url}`,
            `Response: ${response.statusCode} ${response.statusText}`,
            `Response time: ${response.responseTime}ms`,
            `Response size: ${response.size || 0} bytes`,
          ],
        },
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        error,
        shouldRetry: await this.shouldRetryHttpError(error, context),
        metadata: {
          executionTime,
          httpStatusCode: this.extractStatusCodeFromError(error),
          logs: [
            `HTTP request failed: ${error.message}`,
            `Execution time: ${executionTime}ms`,
          ],
        },
      };
    }
  }
  
  /**
   * Validate HTTP node input
   */
  protected async validateNodeSpecificInput(
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    const params = context.node.parameters as any;
    
    // Validate URL
    if (!params.url) {
      result.errors.push({
        code: 'MISSING_URL',
        message: 'HTTP node requires a URL',
        field: 'url',
        severity: 'error',
      });
    } else {
      try {
        new URL(params.url);
      } catch {
        result.errors.push({
          code: 'INVALID_URL',
          message: 'Invalid URL format',
          field: 'url',
          currentValue: params.url,
          severity: 'error',
        });
      }
    }
    
    // Validate HTTP method
    const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'];
    if (params.method && !validMethods.includes(params.method.toUpperCase())) {
      result.errors.push({
        code: 'INVALID_METHOD',
        message: 'Invalid HTTP method',
        field: 'method',
        currentValue: params.method,
        expectedValue: validMethods,
        severity: 'error',
      });
    }
    
    // Validate headers
    if (params.headers && typeof params.headers !== 'object') {
      result.errors.push({
        code: 'INVALID_HEADERS',
        message: 'Headers must be an object',
        field: 'headers',
        severity: 'error',
      });
    }
    
    // Validate authentication
    if (params.auth) {
      const auth = params.auth;
      if (!['basic', 'bearer', 'api-key'].includes(auth.type)) {
        result.errors.push({
          code: 'INVALID_AUTH_TYPE',
          message: 'Invalid authentication type',
          field: 'auth.type',
          currentValue: auth.type,
          expectedValue: ['basic', 'bearer', 'api-key'],
          severity: 'error',
        });
      }
      
      if (auth.type === 'basic' && (!auth.username || !auth.password)) {
        result.errors.push({
          code: 'MISSING_BASIC_AUTH',
          message: 'Basic auth requires username and password',
          field: 'auth',
          severity: 'error',
        });
      }
      
      if (auth.type === 'bearer' && !auth.token) {
        result.errors.push({
          code: 'MISSING_BEARER_TOKEN',
          message: 'Bearer auth requires token',
          field: 'auth.token',
          severity: 'error',
        });
      }
      
      if (auth.type === 'api-key' && !auth.apiKey) {
        result.errors.push({
          code: 'MISSING_API_KEY',
          message: 'API key auth requires apiKey',
          field: 'auth.apiKey',
          severity: 'error',
        });
      }
    }
    
    // Validate timeout
    if (params.timeout && (typeof params.timeout !== 'number' || params.timeout <= 0)) {
      result.warnings.push({
        code: 'INVALID_TIMEOUT',
        message: 'Timeout should be a positive number',
        field: 'timeout',
        suggestion: 'Use a positive number in milliseconds',
      });
    }
  }
  
  /**
   * Validate HTTP response
   */
  protected async validateNodeSpecificOutput(
    outputData: any,
    context: ExecutorContext,
    result: ValidationResult
  ): Promise<void> {
    if (!outputData) {
      result.errors.push({
        code: 'EMPTY_RESPONSE',
        message: 'HTTP response is empty',
        severity: 'error',
      });
      return;
    }
    
    // Validate status code
    if (typeof outputData.statusCode !== 'number') {
      result.errors.push({
        code: 'MISSING_STATUS_CODE',
        message: 'Response missing status code',
        severity: 'error',
      });
    }
    
    // Check for expected content type
    const params = context.node.parameters as any;
    if (params.response?.expectedContentType && outputData.headers) {
      const contentType = outputData.headers['content-type'] || '';
      if (!contentType.includes(params.response.expectedContentType)) {
        result.warnings.push({
          code: 'UNEXPECTED_CONTENT_TYPE',
          message: `Expected content type ${params.response.expectedContentType}, got ${contentType}`,
          suggestion: 'Verify the API endpoint returns expected content type',
        });
      }
    }
  }
  
  // Private helper methods
  
  private parseHttpConfig(context: ExecutorContext): HttpRequestConfig {
    const params = context.node.parameters as any;
    
    return {
      method: (params.method || 'GET').toUpperCase(),
      url: params.url,
      headers: params.headers || {},
      params: params.params || params.queryParams,
      body: params.body || params.data,
      timeout: params.timeout || 30000,
      followRedirects: params.followRedirects !== false,
      maxRedirects: params.maxRedirects || 5,
      validateSSL: params.validateSSL !== false,
      userAgent: params.userAgent || 'XState-Workflow-Engine/1.0',
      auth: params.auth,
      retry: {
        enabled: params.retry?.enabled !== false,
        maxAttempts: params.retry?.maxAttempts || 3,
        delay: params.retry?.delay || 1000,
        backoffStrategy: params.retry?.backoffStrategy || 'exponential',
        retryOn: params.retry?.retryOn || [408, 429, 500, 502, 503, 504],
      },
      response: {
        expectedContentType: params.response?.expectedContentType,
        parseJSON: params.response?.parseJSON !== false,
        includeHeaders: params.response?.includeHeaders || false,
        includeStatusCode: params.response?.includeStatusCode !== false,
        successCodes: params.response?.successCodes || [200, 201, 202, 204],
      },
    };
  }
  
  private async prepareRequest(
    httpConfig: HttpRequestConfig,
    context: ExecutorContext
  ): Promise<any> {
    const requestConfig: any = {
      method: httpConfig.method,
      url: httpConfig.url,
      timeout: httpConfig.timeout,
      maxRedirects: httpConfig.maxRedirects,
      validateStatus: () => true, // We'll handle status validation ourselves
    };
    
    // Add headers
    requestConfig.headers = {
      'User-Agent': httpConfig.userAgent,
      ...httpConfig.headers,
      ...context.headers, // Headers từ execution context
    };
    
    // Add query parameters
    if (httpConfig.params) {
      requestConfig.params = httpConfig.params;
    }
    
    // Add request body
    if (httpConfig.body && ['POST', 'PUT', 'PATCH'].includes(httpConfig.method)) {
      requestConfig.data = httpConfig.body;
      
      // Set content-type if not specified
      if (!requestConfig.headers['Content-Type'] && !requestConfig.headers['content-type']) {
        if (typeof httpConfig.body === 'object') {
          requestConfig.headers['Content-Type'] = 'application/json';
        }
      }
    }
    
    // Add authentication
    if (httpConfig.auth) {
      switch (httpConfig.auth.type) {
        case 'basic':
          requestConfig.auth = {
            username: httpConfig.auth.username,
            password: httpConfig.auth.password,
          };
          break;
          
        case 'bearer':
          requestConfig.headers['Authorization'] = `Bearer ${httpConfig.auth.token}`;
          break;
          
        case 'api-key':
          const headerName = httpConfig.auth.apiKeyHeader || 'X-API-Key';
          requestConfig.headers[headerName] = httpConfig.auth.apiKey;
          break;
      }
    }
    
    return requestConfig;
  }
  
  private async executeHttpRequest(
    requestConfig: any,
    config: NodeExecutionConfig
  ): Promise<HttpResponseData> {
    const startTime = Date.now();
    
    try {
      const response = await firstValueFrom(
        this.httpService.request(requestConfig)
      );
      
      const responseTime = Date.now() - startTime;
      
      return {
        statusCode: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        size: this.calculateResponseSize(response.data),
        responseTime,
        finalUrl: response.config.url,
      };
      
    } catch (error) {
      // Handle axios errors
      if (error.response) {
        const responseTime = Date.now() - startTime;
        
        return {
          statusCode: error.response.status,
          statusText: error.response.statusText,
          headers: error.response.headers,
          data: error.response.data,
          size: this.calculateResponseSize(error.response.data),
          responseTime,
          finalUrl: error.config?.url,
        };
      }
      
      throw error;
    }
  }
  
  private processResponse(
    response: HttpResponseData,
    httpConfig: HttpRequestConfig
  ): any {
    const outputData: any = {
      data: response.data,
    };
    
    // Include status code if requested
    if (httpConfig.response?.includeStatusCode) {
      outputData.statusCode = response.statusCode;
      outputData.statusText = response.statusText;
    }
    
    // Include headers if requested
    if (httpConfig.response?.includeHeaders) {
      outputData.headers = response.headers;
    }
    
    // Include response metadata
    outputData.metadata = {
      responseTime: response.responseTime,
      size: response.size,
      finalUrl: response.finalUrl,
    };
    
    // Parse JSON if requested and content type is JSON
    if (httpConfig.response?.parseJSON && response.headers) {
      const contentType = response.headers['content-type'] || '';
      if (contentType.includes('application/json') && typeof response.data === 'string') {
        try {
          outputData.data = JSON.parse(response.data);
        } catch (error) {
          // Keep original data if JSON parsing fails
          this.logger.warn('Failed to parse JSON response:', error);
        }
      }
    }
    
    return outputData;
  }
  
  private async shouldRetryHttpError(error: any, context: ExecutorContext): Promise<boolean> {
    const params = context.node.parameters as any;
    const retryConfig = params.retry;
    
    if (!retryConfig?.enabled) {
      return false;
    }
    
    // Check if error has status code that should be retried
    if (error.response?.status) {
      const statusCode = error.response.status;
      const retryOn = retryConfig.retryOn || [408, 429, 500, 502, 503, 504];
      return retryOn.includes(statusCode);
    }
    
    // Retry on network errors
    const networkErrors = ['ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 'ENOTFOUND'];
    return networkErrors.some(code => error.code === code || error.message.includes(code));
  }
  
  private extractStatusCodeFromError(error: any): number | undefined {
    return error.response?.status;
  }
  
  private calculateResponseSize(data: any): number {
    if (typeof data === 'string') {
      return Buffer.byteLength(data, 'utf8');
    }
    
    if (typeof data === 'object') {
      return Buffer.byteLength(JSON.stringify(data), 'utf8');
    }
    
    return 0;
  }
}
