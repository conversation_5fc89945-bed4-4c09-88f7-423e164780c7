import { Connection } from '../../entities/connection.entity';
import { Node } from '../../entities/node.entity';

/**
 * Trạng thái thực thi của một node
 */
export interface NodeExecutionState {
  /** ID của node */
  id: string;
  
  /** Trạng thái hiện tại */
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  
  /** Dữ liệu đầu vào */
  inputData: any;
  
  /** Dữ liệu đầu ra */
  outputData: any;
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Thời gian bắt đầu */
  startTime?: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** Số lần retry */
  retryCount: number;
  
  /** Node definition */
  node: Node;
}

/**
 * Metadata của workflow execution
 */
export interface WorkflowExecutionMetadata {
  /** Thời gian bắt đầu */
  startTime: number;
  
  /** Thời gian kết thúc */
  endTime?: number;
  
  /** ID của user thực thi */
  userId: number;
  
  /** Số lần retry tổng */
  retryCount: number;
  
  /** Loại trigger */
  triggerType: 'manual' | 'webhook' | 'schedule';
  
  /** Source trigger */
  source?: string;
  
  /** Webhook ID nếu trigger từ webhook */
  webhookId?: string;
  
  /** Schedule ID nếu trigger từ schedule */
  scheduleId?: string;
  
  /** Priority */
  priority?: number;
}

/**
 * Context của XState workflow machine
 */
export interface WorkflowContext {
  /** ID của workflow */
  workflowId: string;
  
  /** ID của execution */
  executionId: string;
  
  /** Map các node execution states */
  nodes: Map<string, NodeExecutionState>;
  
  /** Danh sách connections */
  connections: Connection[];
  
  /** Node hiện tại đang thực thi */
  currentNode?: string;
  
  /** Dữ liệu output từ các nodes */
  executionData: Map<string, any>;
  
  /** Map các lỗi */
  errors: Map<string, Error>;
  
  /** Dữ liệu trigger ban đầu */
  triggerData: any;
  
  /** Metadata của execution */
  metadata: WorkflowExecutionMetadata;
  
  /** Dependency graph */
  dependencyGraph: Map<string, string[]>;
  
  /** Nodes sẵn sàng thực thi */
  readyNodes: string[];
  
  /** Tùy chọn execution */
  options?: {
    enableSSE?: boolean;
    timeout?: number;
    retryOnFailure?: boolean;
    skipValidation?: boolean;
  };
}

/**
 * Kết quả thực thi node
 */
export interface NodeExecutionResult {
  /** Thành công hay không */
  success: boolean;
  
  /** Dữ liệu output */
  outputData?: any;
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Metadata bổ sung */
  metadata?: {
    executionTime: number;
    memoryUsage?: number;
    [key: string]: any;
  };
}

/**
 * Kết quả từ LangGraph agent
 */
export interface LangGraphExecutionResult {
  /** Thành công hay không */
  success: boolean;
  
  /** Kết quả từ agent */
  result?: any;
  
  /** Messages từ agent conversation */
  messages?: any[];
  
  /** Lỗi nếu có */
  error?: Error;
  
  /** Metadata từ LangGraph */
  metadata?: {
    threadId: string;
    checkpointId?: string;
    executionTime: number;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  };
}

/**
 * Configuration cho node execution
 */
export interface NodeExecutionConfig {
  /** Timeout cho node (ms) */
  timeout?: number;
  
  /** Có retry khi fail không */
  retryOnFail?: boolean;
  
  /** Số lần retry tối đa */
  maxRetries?: number;
  
  /** Thời gian chờ giữa các retry (ms) */
  retryDelay?: number;
  
  /** Có skip validation không */
  skipValidation?: boolean;
  
  /** Có gửi SSE events không */
  enableSSE?: boolean;
}
